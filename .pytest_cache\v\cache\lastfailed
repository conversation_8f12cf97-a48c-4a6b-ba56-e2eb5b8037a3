{"tests/test_outline.py::TestOutlineAPI::test_upload_invalid_file_type": true, "tests/test_outline.py::TestOutlineAPI::test_upload_empty_file": true, "tests/test_outline.py::TestOutlineAPI::test_generate_outline_success": true, "tests/test_outline.py::TestOutlineAPI::test_get_nonexistent_task": true, "tests/test_rag.py::TestRAGService::test_build_index_success": true, "tests/test_rag.py::TestRAGService::test_query_retrieval_mode": true, "tests/test_api_v2_integration.py::TestClient": true, "tests/test_api_v2_integration.py::TestRAGV2API": true, "tests/test_api_v2_integration.py::TestConversationV2API": true, "tests/test_api_v2_integration.py::TestAPIV2Integration": true, "tests/test_api_v2_integration.py::TestRAGV2API::test_get_collections": true, "tests/test_api_v2_integration.py::TestRAGV2API::test_get_collection_info": true, "tests/test_api_v2_integration.py::TestRAGV2API::test_delete_documents_by_course": true, "tests/test_api_v2_integration.py::TestRAGV2API::test_delete_documents_by_material": true, "tests/test_api_v2_integration.py::TestRAGV2API::test_count_documents": true, "tests/test_api_v2_integration.py::TestConversationV2API::test_intelligent_chat_simple_engine": true, "tests/test_api_v2_integration.py::TestConversationV2API::test_intelligent_chat_condense_plus_context_engine": true, "tests/test_outline.py": true}